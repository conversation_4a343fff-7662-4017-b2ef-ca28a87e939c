<div class="pt-28 pb-14 inline-flex flex-col justify-start items-center gap-20">
    <div class="self-stretch flex flex-col justify-start items-center gap-5">
        <div
            class="px-5 py-1.5 bg-gradient-to-l from-rose-950 to-red-600 rounded-[40px] inline-flex justify-center items-center gap-2.5">
            <div class="text-center justify-start text-white text-lg font-normal font-['Manrope'] leading-relaxed">Just
                one more step to unlimited streaming?</div>
        </div>
        <div
            class="text-center justify-start text-white text-4xl font-bold font-['Manrope'] leading-9 [text-shadow:_0px_25px_50px_rgb(0_0_0_/_0.25)]">
            Almost There! Secure Your Netflix Access Today</div>
    </div>
    <div class="self-stretch inline-flex justify-center items-start gap-10">
        <div
            class="w-[800px] p-12 bg-stone-950 rounded-xl outline outline-1 outline-offset-[-1px] outline-neutral-800 inline-flex flex-col justify-start items-start gap-10">
            <div class="self-stretch inline-flex justify-start items-start gap-12">
                <div class="flex-1 inline-flex flex-col justify-start items-start gap-4">
                    <div
                        class="self-stretch justify-start text-white text-lg font-semibold font-['Manrope'] leading-relaxed">
                        Full Name</div>
                    <div
                        class="self-stretch p-5 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 inline-flex justify-start items-center gap-20 overflow-hidden">
                        <div
                            class="flex-1 justify-start text-neutral-400 text-lg font-normal font-['Manrope'] leading-relaxed">
                            John Doe</div>
                    </div>
                </div>
            </div>
            <div class="self-stretch inline-flex justify-start items-start gap-12">
                <div class="flex-1 inline-flex flex-col justify-start items-start gap-4">
                    <div
                        class="self-stretch justify-start text-white text-lg font-semibold font-['Manrope'] leading-relaxed">
                        Email Address</div>
                    <div
                        class="self-stretch p-5 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 inline-flex justify-start items-center gap-20">
                        <div
                            class="flex-1 justify-start text-neutral-400 text-lg font-normal font-['Manrope'] leading-relaxed">
                            <EMAIL></div>
                    </div>
                </div>
            </div>
            <div class="self-stretch flex flex-col justify-start items-start gap-4">
                <div
                    class="self-stretch justify-start text-white text-lg font-semibold font-['Manrope'] leading-relaxed">
                    Country</div>
                <div class="self-stretch inline-flex justify-between items-center">
                    <div
                        class="flex-1 h-16 px-3 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-between items-center">
                        <div class="flex justify-start items-center gap-3.5">
                            <div class="w-10 rounded-[5px] flex justify-center items-center gap-2.5 overflow-hidden">
                                <div class="w-10 h-7 relative overflow-hidden">
                                    <div class="w-10 h-7 left-0 top-[0.50px] absolute bg-rose-700"></div>
                                    <div class="w-4 h-3.5 left-[13.12px] top-[5.86px] absolute bg-green-800"></div>
                                </div>
                            </div>
                            <div
                                class="justify-start text-neutral-400 text-lg font-normal font-['Manrope'] leading-relaxed">
                                Morocco</div>
                        </div>
                        <div class="w-6 h-6 relative overflow-hidden">
                            <div
                                class="w-1 h-3 left-[6px] top-[13.64px] absolute origin-top-left -rotate-90 outline outline-2 outline-offset-[-1px] outline-neutral-400">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="self-stretch inline-flex justify-start items-center gap-16">
                <div class="px-6 py-4 bg-red-600 rounded-lg flex justify-start items-start gap-2.5">
                    <div class="justify-start text-white text-lg font-semibold font-['Manrope'] leading-relaxed">
                        Complete Purchase</div>
                </div>
            </div>
        </div>
        <div class="inline-flex flex-col justify-center items-start gap-10">
            <div
                class="w-[480px] p-12 relative bg-stone-950 rounded-xl outline outline-1 outline-offset-[-1px] outline-neutral-800 flex flex-col justify-start items-start gap-12">
                <div class="self-stretch flex flex-col justify-start items-start gap-4">
                    <div class="self-stretch justify-start text-white text-xl font-bold font-['Manrope'] leading-loose">
                        Basic Plan</div>
                    <div
                        class="self-stretch justify-start text-white text-base font-semibold font-['Manrope'] leading-normal">
                        🔔 No surprises—review before you buy.!</div>
                    <div
                        class="self-stretch justify-start text-neutral-400 text-base font-normal font-['Manrope'] leading-normal">
                        Ideal for the budget-conscious viewer who wants access to Netflix’s library in Standard
                        Definition without any extra bells and whistles.</div>
                </div>
                <div class="self-stretch rounded-full inline-flex justify-between items-end">
                    <div class="justify-start text-white text-xl font-semibold font-['Manrope'] leading-loose">Price:
                    </div>
                    <div class="flex justify-center items-end gap-1">
                        <div class="justify-start text-white text-4xl font-semibold font-['Manrope'] leading-7">$5.99
                        </div>
                        <div class="justify-start text-neutral-400 text-lg font-medium font-['Manrope'] leading-3">
                            /month</div>
                    </div>
                </div>
                <div
                    class="px-4 py-1 left-[315px] top-[20px] absolute bg-gradient-to-l from-rose-950 to-red-600 rounded-[40px] inline-flex justify-center items-center gap-2.5">
                    <div
                        class="text-center justify-start text-white text-base font-medium font-['Manrope'] leading-normal">
                        Your Selection</div>
                </div>
            </div>
            <div
                class="w-[480px] p-12 bg-stone-950 rounded-xl outline outline-1 outline-offset-[-1px] outline-neutral-800 flex flex-col justify-start items-start gap-12">
                <div class="self-stretch flex flex-col justify-start items-start gap-4">
                    <div class="flex flex-col justify-start items-start gap-4">
                        <div class="justify-start text-white text-xl font-semibold font-['Manrope'] leading-loose">
                            Choose a Payment Method</div>
                        <div
                            class="w-96 justify-start text-neutral-400 text-base font-normal font-['Manrope'] leading-normal">
                            We accept all major cards & PayPal</div>
                    </div>
                    <div class="flex flex-col justify-start items-start">
                        <div class="w-96 h-11 py-2.5 inline-flex justify-start items-center gap-4">
                            <div class="flex justify-start items-center overflow-hidden">
                                <div class="w-6 h-6 bg-red-600"></div>
                            </div>
                            <div
                                class="justify-center text-white text-base font-medium font-['Manrope'] leading-normal">
                                Credit & Debit Cards (via Stripe)</div>
                        </div>
                        <div class="w-96 h-11 py-2.5 inline-flex justify-start items-center gap-4">
                            <div class="flex justify-start items-center overflow-hidden">
                                <div class="w-6 h-6 rounded-full border-2 border-neutral-400"></div>
                            </div>
                            <div
                                class="justify-center text-white text-base font-medium font-['Manrope'] leading-normal">
                                Pay with PayPal</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>