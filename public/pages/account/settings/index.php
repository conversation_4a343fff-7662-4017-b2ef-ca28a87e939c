<div class="w-full pt-28 pb-14 lp-sm:px-20 tb-lg:px-10 px-4">
    <div class="flex flex-col justify-start items-start gap-10">
        <?php include __DIR__ . "/../hero.php"; ?>
        <!-- Settings Section -->
        <div
            class="w-full p-12 bg-stone-950 rounded-xl outline outline-1 outline-offset-[-1px] outline-neutral-800 inline-flex justify-center items-start gap-12">
            <!-- Sidebar Navigation -->
            <div class="w-72 inline-flex flex-col justify-start items-start gap-7">
                <button type="button" onclick="showSection('profile')"
                    class="self-stretch px-6 py-3.5 bg-red-600 rounded-[10px] outline outline-2 outline-red-500 inline-flex justify-start items-center gap-2.5 focus:bg-red-500 focus:outline-red-400 focus:shadow-lg focus:shadow-red-600/20 transition-all duration-300 settings-nav-btn active"
                    data-section="profile">
                    <div class="justify-start text-white text-lg font-medium font-['Manrope'] leading-relaxed">Profile
                        Details</div>
                </button>
                <button type="button" onclick="showSection('password')"
                    class="self-stretch px-6 py-3.5 bg-stone-900 rounded-[10px] outline outline-2 outline-neutral-800 inline-flex justify-start items-center gap-2.5 transition-all duration-300 settings-nav-btn"
                    data-section="password">
                    <div class="justify-start text-white text-lg font-medium font-['Manrope'] leading-relaxed">Change
                        Password</div>
                </button>
                <button type="button" onclick="showSection('support')"
                    class="self-stretch px-6 py-3.5 bg-stone-900 rounded-[10px] outline outline-2 outline-neutral-800 inline-flex justify-start items-center gap-2.5 transition-all duration-300 settings-nav-btn"
                    data-section="support">
                    <div class="justify-start text-white text-lg font-medium font-['Manrope'] leading-relaxed">Support
                        Tickets</div>
                </button>
            </div>
            <!-- Content Sections -->
            <div class="flex-1">
                <!-- Profile Section -->
                <?php include __DIR__ . "/profile-details.php"; ?>

                <!-- Password Change Section -->
                <?php include __DIR__ . "/change-password.php"; ?>

                <!-- Support Tickets Section -->
                <?php include __DIR__ . "/support-tickets.php"; ?>

            </div>
        </div>
    </div>
</div>