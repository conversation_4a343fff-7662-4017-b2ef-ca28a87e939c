<div id="password-section" class="settings-section hidden">
    <form action="/account/settings/change-password" method="POST"
        class="flex-1 inline-flex flex-col justify-start items-start gap-12 w-full">
        <div class="self-stretch flex flex-col justify-start items-start gap-5 border-b custom-border-image">
            <div class="self-stretch justify-start text-white text-2xl font-bold font-['Manrope'] leading-9">
                Change Password</div>
        </div>

        <!-- Current Password Field -->
        <div class="self-stretch inline-flex justify-start items-start gap-12">
            <div class="flex-1 flex flex-col justify-start items-start gap-4">
                <label for="current-password"
                    class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                    Current Password
                </label>
                <div
                    class="self-stretch mb-md:p-5 p-4 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-between items-center gap-4 overflow-hidden">
                    <input type="password" name="current_password" id="current-password"
                        placeholder="Enter your current password"
                        class="flex-1 sha text-neutral-400 font-normal leading-relaxed font-manrope bg-transparent border-none outline-none mb-md:text-lg text-sm"
                        required />
                    <button type="button" class="password-toggle" data-target="current-password">
                        <?php icon('mdi:eye-outline', 'text-neutral-400 text-xl hover:text-white transition-colors duration-200'); ?>
                    </button>
                </div>
            </div>
        </div>

        <!-- New Password Field -->
        <div class="self-stretch inline-flex justify-start items-start gap-12">
            <div class="flex-1 flex flex-col justify-start items-start gap-4">
                <label for="new-password"
                    class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                    New Password
                </label>
                <div
                    class="self-stretch mb-md:p-5 p-4 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-between items-center gap-4 overflow-hidden">
                    <input type="password" name="new_password" id="new-password" placeholder="Enter your new password"
                        class="flex-1 text-neutral-400 font-normal leading-relaxed font-manrope bg-transparent border-none outline-none mb-md:text-lg text-sm"
                        required />
                    <button type="button" class="password-toggle" data-target="new-password">
                        <?php icon('mdi:eye-outline', 'text-neutral-400 text-xl hover:text-white transition-colors duration-200'); ?>
                    </button>
                </div>
            </div>
        </div>

        <!-- Confirm New Password Field -->
        <div class="self-stretch inline-flex justify-start items-start gap-12">
            <div class="flex-1 flex flex-col justify-start items-start gap-4">
                <label for="confirm-password"
                    class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                    Confirm New Password
                </label>
                <div
                    class="self-stretch mb-md:p-5 p-4 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-between items-center gap-4 overflow-hidden">
                    <input type="password" name="confirm_password" id="confirm-password"
                        placeholder="Confirm your new password"
                        class="flex-1 text-neutral-400 font-normal leading-relaxed font-manrope bg-transparent border-none outline-none mb-md:text-lg text-sm"
                        required />
                    <button type="button" class="password-toggle" data-target="confirm-password">
                        <?php icon('mdi:eye-outline', 'text-neutral-400 text-xl hover:text-white transition-colors duration-200'); ?>
                    </button>
                </div>
            </div>
        </div>

        <!-- Password Requirements -->
        <div class="self-stretch flex flex-col justify-start items-start gap-3">
            <div class="text-white text-base font-semibold font-['Manrope'] leading-relaxed">
                Password Requirements:
            </div>
            <ul class="text-neutral-400 text-sm font-normal font-['Manrope'] leading-relaxed space-y-1 ml-4">
                <li class="flex items-center gap-2 text-base password-requirement">
                    <?php icon('mdi:check-circle-outline', 'text-neutral-500 text-lg'); ?>
                    At least 8 characters long
                </li>
                <li class="flex items-center gap-2 text-base password-requirement">
                    <?php icon('mdi:check-circle-outline', 'text-neutral-500 text-lg'); ?>
                    Contains at least one uppercase letter
                </li>
                <li class="flex items-center gap-2 text-base password-requirement">
                    <?php icon('mdi:check-circle-outline', 'text-neutral-500 text-lg'); ?>
                    Contains at least one lowercase letter
                </li>
                <li class="flex items-center gap-2 text-base password-requirement">
                    <?php icon('mdi:check-circle-outline', 'text-neutral-500 text-lg'); ?>
                    Contains at least one number
                </li>
            </ul>
        </div>

        <!-- Form Action Buttons -->
        <div class="self-stretch inline-flex justify-end items-center gap-5">
            <?php renderCancelButton("Cancel", "/account/settings"); ?>
            <?php renderSaveButton("Update"); ?>
        </div>
    </form>
</div>