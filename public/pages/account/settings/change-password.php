<div id="password-section" class="settings-section hidden">
    <form action="/account/settings/change-password" method="POST"
        class="flex-1 inline-flex flex-col justify-start items-start gap-12 w-full">
        <div class="self-stretch flex flex-col justify-start items-start gap-5 border-b custom-border-image">
            <div class="self-stretch justify-start text-white text-2xl font-bold font-['Manrope'] leading-9">
                Change Password</div>
        </div>

        <!-- Current Password Field -->
        <div class="self-stretch inline-flex justify-start items-start gap-12">
            <?php renderPasswordField("Current Password", "Enter your current password", "current_password", "current-password"); ?>
        </div>

        <!-- New Password Field -->
        <div class="self-stretch inline-flex justify-start items-start gap-12">
            <?php renderPasswordField("New Password", "Enter your new password", "new_password", "new-password"); ?>
        </div>

        <!-- Confirm New Password Field -->
        <div class="self-stretch inline-flex justify-start items-start gap-12">
            <?php renderPasswordField("Confirm New Password", "Confirm your new password", "confirm_password", "confirm-password"); ?>
        </div>

        <!-- Password Requirements -->
        <div class="self-stretch flex flex-col justify-start items-start gap-3">
            <div class="text-white text-base font-semibold font-['Manrope'] leading-relaxed">
                Password Requirements:
            </div>
            <ul class="text-neutral-400 text-base font-normal font-['Manrope'] leading-relaxed space-y-1 ml-4">
                <li class="flex items-center gap-2 text-base password-requirement">
                    <?php icon('mdi:check-circle-outline', 'text-neutral-500 text-lg'); ?>
                    At least 8 characters long
                </li>
                <li class="flex items-center gap-2 text-base password-requirement">
                    <?php icon('mdi:check-circle-outline', 'text-neutral-500 text-lg'); ?>
                    Contains at least one uppercase letter
                </li>
                <li class="flex items-center gap-2 text-base password-requirement">
                    <?php icon('mdi:check-circle-outline', 'text-neutral-500 text-lg'); ?>
                    Contains at least one lowercase letter
                </li>
                <li class="flex items-center gap-2 text-base password-requirement">
                    <?php icon('mdi:check-circle-outline', 'text-neutral-500 text-lg'); ?>
                    Contains at least one number
                </li>
            </ul>
        </div>

        <!-- Form Action Buttons -->
        <div class="self-stretch inline-flex justify-end items-center gap-5">
            <?php renderCancelButton("Cancel", "/account/settings"); ?>
            <?php renderSaveButton("Update"); ?>
        </div>
    </form>
</div>