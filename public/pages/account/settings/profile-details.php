<div id="profile-section" class="settings-section">
    <form action="/account/settings/update" method="POST"
        class="flex-1 inline-flex flex-col justify-start items-start gap-12 w-full">
        <div class="self-stretch flex flex-col justify-start items-start gap-5 border-b custom-border-image">
            <div class="self-stretch justify-start text-white text-2xl font-bold font-['Manrope'] leading-9">
                Edit Profile</div>
        </div>

        <!-- Full Name Field -->
        <div class="self-stretch inline-flex justify-start items-start gap-12">
            <div class="flex-1 flex flex-col justify-start items-start gap-4">
                <label for="full-name"
                    class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                    Full Name
                </label>
                <div
                    class="self-stretch mb-md:p-5 p-4 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-start items-center gap-20 overflow-hidden">
                    <input type="text" name="full_name" id="full-name" value="<?= htmlspecialchars($user['name']) ?>"
                        placeholder="Enter your full name"
                        class="flex-1 text-neutral-400 font-normal leading-relaxed font-manrope bg-transparent border-none outline-none mb-md:text-lg text-sm"
                        required />
                </div>
            </div>
        </div>

        <!-- Email Address Field -->
        <div class="self-stretch inline-flex justify-start items-start gap-12">
            <div class="flex-1 flex flex-col justify-start items-start gap-4">
                <label for="email-address"
                    class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                    Email Address
                </label>
                <div
                    class="self-stretch mb-md:p-5 p-4 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-start items-center gap-20 overflow-hidden">
                    <input type="email" name="email" id="email-address" value="<?= htmlspecialchars($user['email']) ?>"
                        placeholder="Enter your email address"
                        class="flex-1 text-neutral-400 font-normal leading-relaxed font-manrope bg-transparent border-none outline-none mb-md:text-lg text-sm"
                        required />
                </div>
            </div>
        </div>

        <!-- Phone Number Field -->
        <div class="self-stretch inline-flex justify-start items-start gap-12">
            <div class="flex-1 flex flex-col justify-start items-start gap-4">
                <label for="phone-number"
                    class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                    Phone Number
                </label>
                <div class="self-stretch flex justify-start items-stretch mb-md:gap-4 gap-3">
                    <?php renderCountryCode(); ?>
                    <div class="flex-1">
                        <div
                            class="self-stretch mb-md:p-5 p-4 bg-neutral-900 rounded-r-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-start items-center gap-20 overflow-hidden">
                            <input type="tel" name="phone" id="phone-number" placeholder="Enter phone number"
                                class="flex-1 text-neutral-400 font-normal leading-relaxed font-manrope bg-transparent border-none outline-none mb-md:text-lg text-sm" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Action Buttons -->
        <div class="self-stretch inline-flex justify-end items-center gap-5">
            <?php renderCancelButton("Cancel", "/account/settings"); ?>
            <?php renderSaveButton("Save Changes"); ?>
        </div>
    </form>
</div>