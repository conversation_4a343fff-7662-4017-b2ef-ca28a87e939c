<div class="w-full pt-28 pb-14 lp-sm:px-20 tb-lg:px-10 px-4">
    <div class="flex flex-col justify-start items-start gap-10">
        <?php include __DIR__ . "/../hero.php"; ?>
        <!-- Subscriptions Section -->
        <div
            class="w-full p-6 lg:p-12 bg-stone-950 rounded-xl outline outline-1 outline-offset-[-1px] outline-neutral-800 flex flex-col justify-start items-center gap-8 lg:gap-12 hover:outline-neutral-700 transition-colors duration-300">
            <div class="flex flex-col justify-start items-start gap-5 w-full pb-4 border-b custom-border-image">
                <div class="w-full text-left text-white text-xl lg:text-2xl font-bold font-['Manrope'] leading-9">
                    Manage your active plans and renewal dates</div>
            </div>
            <!-- Dynamic Subscription Cards -->
            <div class="w-full grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                <?php foreach ($subscriptions as $subscription): ?>
                    <?php
                    $isActive = $subscription['status'] === 'active';
                    $statusColor = $isActive ? 'green' : 'red';
                    $expiryDate = date('M d, Y', strtotime($subscription['expiresOn']));
                    $isExpiringSoon = strtotime($subscription['expiresOn']) - time() < (30 * 24 * 60 * 60); // 30 days
                    ?>
                    <div
                        class="w-full p-7 bg-neutral-900 rounded-xl outline outline-1 outline-offset-[-1px] outline-neutral-800 hover:outline-neutral-700 hover:bg-neutral-800 transition-all duration-300 flex flex-col justify-start items-start gap-7 group">
                        <!-- Header with expiry and status -->
                        <div class="self-stretch flex justify-between items-start">
                            <div class="flex justify-start items-center gap-2.5">
                                <div class="text-neutral-400 text-base font-normal font-['Manrope'] leading-normal">
                                    Expires on:</div>
                                <div
                                    class="px-4 py-2.5 bg-zinc-800 rounded-[57px] flex justify-center items-center gap-2.5">
                                    <div class="text-neutral-400 text-base font-normal font-['Manrope'] leading-normal">
                                        <?= $expiryDate ?>
                                    </div>
                                </div>
                            </div>
                            <div
                                class="px-4 py-1 bg-<?= $statusColor ?>-500/20 rounded-[40px] flex justify-center items-center gap-2.5">
                                <?php if ($isActive): ?>
                                    <div class="w-2 h-2 bg-<?= $statusColor ?>-500 rounded-full"></div>
                                <?php endif; ?>
                                <div
                                    class="text-<?= $statusColor ?>-500 text-base font-medium font-['Manrope'] leading-normal">
                                    <?= $subscription['status'] ?>
                                </div>
                            </div>
                        </div>

                        <!-- Plan name -->
                        <div class="self-stretch flex flex-col justify-start items-start gap-4">
                            <div class="self-stretch text-white text-xl font-bold font-['Manrope'] leading-loose">
                                <?= htmlspecialchars($subscription['plan']) ?>
                            </div>
                        </div>

                        <!-- Action button -->
                        <button
                            onclick="handleSubscriptionAction(<?= $subscription['id'] ?>, '<?= $subscription['status'] ?>')"
                            class="self-stretch h-14 px-6 py-4 bg-red-600 hover:bg-red-700 rounded-lg flex justify-center items-center gap-2.5 transition-all duration-300 Manage your active plans and renewal dates
 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-600 focus:ring-offset-2 focus:ring-offset-neutral-900 group">
                            <?= icon("mdi:autorenew", "text-white text-lg group-hover:rotate-180 transition-transform duration-500") ?>
                            <div class="text-white text-lg font-semibold font-['Manrope'] leading-relaxed">
                                <?= $isActive ? 'Renew' : 'Reactivate' ?>
                            </div>
                        </button>

                        <!-- Pricing -->
                        <div class="self-stretch rounded-full flex justify-between items-end">
                            <div class="text-white text-xl font-semibold font-['Manrope'] leading-loose">
                                Total:</div>
                            <div class="flex justify-center items-end gap-1">
                                <div class="text-white text-4xl font-semibold font-['Manrope'] leading-7">
                                    <?= htmlspecialchars($subscription['price']) ?>
                                </div>
                                <div class="text-neutral-400 text-lg font-medium font-['Manrope'] leading-3">
                                    /<?= htmlspecialchars($subscription['period']) ?></div>
                            </div>
                        </div>


                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>