<div class="w-full pt-28 pb-14 inline-flex flex-col justify-start items-center gap-20">
    <div class="self-stretch flex flex-col justify-start items-center gap-5">
        <div
            class="text-center justify-start text-white text-4xl font-bold font-['Manrope'] leading-9 [text-shadow:_0px_25px_50px_rgb(0_0_0_/_0.25)]">
            Create Your NetflixCheap Account</div>
        <div class="text-center justify-start text-neutral-400 text-lg font-normal font-['Manrope'] leading-relaxed">
            Join thousands of streamers getting unbeatable Netflix subscriptions.</div>
    </div>
    <div class="self-stretch flex flex-col justify-start items-center gap-10">
        <div
            class="w-full max-w-[700px] p-12 bg-stone-950 rounded-xl outline outline-1 outline-offset-[-1px] outline-neutral-800 flex flex-col justify-start items-center gap-12">
            <div class="flex flex-col justify-start items-center gap-7">
                <div class="inline-flex justify-center items-center gap-[3px]">
                    <div class="flex justify-start items-center gap-1.5 overflow-hidden">
                        <div class="w-11 h-12 bg-red-600"></div>
                        <div class="w-4 h-4 bg-white"></div>
                        <div class="w-5 h-6 bg-orange-50"></div>
                        <div class="justify-start"><span
                                class="text-white text-4xl font-extrabold font-['Open_Sans'] leading-[54px]">Netflix</span><span
                                class="text-white text-4xl font-semibold font-['Open_Sans'] leading-[54px]">Cheap</span>
                        </div>
                    </div>
                </div>
                <div class="w-[600px] h-28 inline-flex justify-start items-start gap-12">
                    <div class="flex-1 inline-flex flex-col justify-start items-start gap-4">
                        <div
                            class="self-stretch justify-start text-white text-lg font-semibold font-['Manrope'] leading-relaxed">
                            Full Name</div>
                        <div
                            class="self-stretch p-5 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 inline-flex justify-start items-center gap-20 overflow-hidden">
                            <div
                                class="flex-1 justify-start text-neutral-400 text-lg font-normal font-['Manrope'] leading-relaxed">
                                Jhon Doe</div>
                        </div>
                    </div>
                </div>
                <div class="w-[600px] h-28 inline-flex justify-start items-start gap-12">
                    <div class="flex-1 inline-flex flex-col justify-start items-start gap-4">
                        <div
                            class="self-stretch justify-start text-white text-lg font-semibold font-['Manrope'] leading-relaxed">
                            Email Address</div>
                        <div
                            class="self-stretch p-5 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 inline-flex justify-start items-center gap-20 overflow-hidden">
                            <div
                                class="flex-1 justify-start text-neutral-400 text-lg font-normal font-['Manrope'] leading-relaxed">
                                <EMAIL></div>
                        </div>
                    </div>
                </div>
                <div class="w-[600px] h-28 inline-flex justify-start items-start gap-12">
                    <div class="flex-1 inline-flex flex-col justify-start items-start gap-4">
                        <div
                            class="self-stretch justify-start text-white text-lg font-semibold font-['Manrope'] leading-relaxed">
                            Password</div>
                        <div
                            class="self-stretch h-16 p-5 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 inline-flex justify-between items-center">
                            <div
                                class="justify-center text-neutral-400 text-lg font-normal font-['Manrope'] leading-relaxed">
                                ••••••••••••</div>
                            <div class="w-6 h-6 relative overflow-hidden">
                                <div
                                    class="w-5 h-4 left-[1.93px] top-[3px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-white">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-[600px] h-28 inline-flex justify-start items-start gap-12">
                    <div class="flex-1 inline-flex flex-col justify-start items-start gap-4">
                        <div
                            class="self-stretch justify-start text-white text-lg font-semibold font-['Manrope'] leading-relaxed">
                            Confirm Password</div>
                        <div
                            class="self-stretch h-16 p-5 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 inline-flex justify-between items-center">
                            <div
                                class="justify-center text-neutral-400 text-lg font-normal font-['Manrope'] leading-relaxed">
                                ••••••••••••</div>
                            <div class="w-6 h-6 relative overflow-hidden">
                                <div
                                    class="w-5 h-4 left-[1.93px] top-[3px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-white">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="self-stretch flex flex-col justify-start items-start gap-4">
                    <div
                        class="self-stretch justify-start text-white text-lg font-semibold font-['Manrope'] leading-relaxed">
                        Country</div>
                    <div class="self-stretch inline-flex justify-between items-center">
                        <div
                            class="flex-1 h-16 px-3 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-between items-center">
                            <div class="flex justify-start items-center gap-3.5">
                                <div
                                    class="w-10 rounded-[5px] flex justify-center items-center gap-2.5 overflow-hidden">
                                    <div class="w-10 h-7 relative overflow-hidden">
                                        <div class="w-10 h-7 left-0 top-[0.50px] absolute bg-rose-700"></div>
                                        <div class="w-4 h-3.5 left-[13.12px] top-[5.86px] absolute bg-green-800"></div>
                                    </div>
                                </div>
                                <div
                                    class="justify-start text-neutral-400 text-lg font-normal font-['Manrope'] leading-relaxed">
                                    Select your country</div>
                            </div>
                            <div class="w-6 h-6 relative overflow-hidden">
                                <div
                                    class="w-1 h-3 left-[6px] top-[13.64px] absolute origin-top-left -rotate-90 outline outline-2 outline-offset-[-1px] outline-neutral-400">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-[600px] px-6 py-4 bg-red-600 rounded-lg inline-flex justify-center items-center gap-2.5">
                    <div class="justify-start text-white text-lg font-semibold font-['Manrope'] leading-relaxed">Create
                        Account</div>
                </div>
                <div class="text-center justify-start"><span
                        class="text-neutral-400 text-lg font-normal font-['Manrope'] leading-relaxed">Already have an
                        account? </span><span
                        class="text-red-600 text-lg font-medium font-['Manrope'] leading-relaxed">Sign in here</span>
                </div>
            </div>
        </div>
        <div
            class="w-[700px] p-12 bg-stone-950 rounded-xl outline outline-1 outline-offset-[-1px] outline-neutral-800 flex flex-col justify-start items-center gap-12">
            <div class="self-stretch inline-flex justify-between items-center">
                <div class="text-center justify-start text-white text-xl font-semibold font-['Manrope'] leading-loose">
                    Why NetflixCheap?</div>
                <div class="inline-flex flex-col justify-center items-start gap-7">
                    <div class="inline-flex justify-start items-center gap-2.5">
                        <div class="w-5 h-5 bg-red-600"></div>
                        <div
                            class="text-center justify-start text-neutral-400 text-lg font-normal font-['Manrope'] leading-relaxed">
                            Best prices, guaranteed</div>
                    </div>
                    <div class="inline-flex justify-start items-center gap-2.5">
                        <div class="w-5 h-5 bg-red-600"></div>
                        <div
                            class="text-center justify-start text-neutral-400 text-lg font-normal font-['Manrope'] leading-relaxed">
                            Instant access - No waiting</div>
                    </div>
                    <div class="inline-flex justify-start items-center gap-2.5">
                        <div class="w-5 h-5 bg-red-600"></div>
                        <div
                            class="text-center justify-start text-neutral-400 text-lg font-normal font-['Manrope'] leading-relaxed">
                            Secure payments, 24/7 support</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>