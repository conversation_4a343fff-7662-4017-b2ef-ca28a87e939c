<div
    class="w-full tb-sm:pt-28 pt-14 tb-sm:pb-14 p-6 lp-sm:px-20 tb-lg:px-10 px-4 flex flex-col justify-start items-center tb-sm:gap-20 gap-10">
    <div class="self-stretch flex flex-col justify-start items-center gap-5">
        <div
            class="text-center justify-start text-white mb-md:text-4xl text-2xl font-bold font-['Manrope'] leading-9 [text-shadow:_0px_25px_50px_rgb(0_0_0_/_0.25)]">
            Welcome Back to NetflixCheap!</div>
        <div
            class="text-center justify-start text-neutral-400 mb-md:text-lg text-sm font-normal font-['Manrope'] leading-relaxed px-4">
            Sign in to manage your subscriptions and keep streaming.</div>
    </div>
    <div class="self-stretch flex justify-center items-start gap-10">
        <form action="/login" method="POST"
            class="w-full max-w-[700px] mb-md:p-12 p-6 bg-stone-950 rounded-xl outline outline-1 outline-offset-[-1px] outline-neutral-800 flex flex-col justify-start items-center mb-md:gap-12 gap-8">
            <div class="flex flex-col justify-start items-center gap-7 w-full">
                <?php renderLogo(false, 'flex justify-center items-center gap-[3px]'); ?>

                <!-- Email Field -->
                <div class="w-full">
                    <?php renderInputField("Email Address", "<EMAIL>", "email", "email", "email") ?>
                </div>

                <!-- Password Field -->
                <div class="w-full">
                    <?php renderPasswordField("Password", "Enter your password", "password", "password") ?>
                </div>

                <!-- Sign In Button -->
                <div class="w-full">
                    <?php renderSubmitButton("Sign In", "p-3 shadow-lg w-full") ?>
                </div>

                <div
                    class="text-center justify-start text-neutral-400 text-base font-normal font-['Manrope'] leading-normal">
                    <a href="/forgot-password"
                        class="hover:text-white transition-colors hover:scale-105 transform duration-300 inline-block">Forgot
                        your password?</a>
                </div>
            </div>

            <div class="flex flex-col justify-start items-center gap-7 w-full">
                <div
                    class="text-center justify-start text-white mb-md:text-xl text-lg font-semibold font-['Manrope'] leading-loose">
                    New to NetflixCheap?</div>
                <div class="w-full">
                    <?php renderSecondaryButton("Create an account", "/sign-up") ?>
                </div>
            </div>
        </form>
    </div>
</div>