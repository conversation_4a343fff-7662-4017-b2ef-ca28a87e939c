<?php
function renderPasswordField(string $label, string $placeholder, string $name = "", string $id = "", bool $required = true, string $customClasses = ''): void
{
    // Default password wrapper classes
    $defaultWrapperClasses = 'self-stretch mb-md:p-5 p-4 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-between items-center gap-4 overflow-hidden';

    // Merge default classes with custom classes using twMerge
    $wrapperClasses = twMerge($defaultWrapperClasses, $customClasses);

    ?>
    <div class="flex-1 flex flex-col justify-start items-start gap-4">
        <?php if (!empty($label)): ?>
            <label for="<?= htmlspecialchars($id) ?>"
                class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                <?= htmlspecialchars($label) ?>
            </label>
        <?php endif; ?>
        <div class="<?= htmlspecialchars($wrapperClasses) ?>">
            <input type="password" name="<?= htmlspecialchars($name) ?>" id="<?= htmlspecialchars($id) ?>"
                placeholder="<?= htmlspecialchars($placeholder) ?>"
                class="flex-1 text-neutral-400 font-normal leading-relaxed font-manrope bg-transparent border-none outline-none mb-md:text-lg text-sm"
                <?= $required ? 'required' : '' ?> />
            <button type="button"
                class="password-toggle flex-shrink-0 p-1 hover:bg-neutral-800 rounded transition-colors leading-[0]"
                data-target="<?= htmlspecialchars($id) ?>">
                <?= icon("mdi:eye-outline", "text-neutral-400 mb-md:text-xl text-lg leading-[0]") ?>
            </button>
        </div>
    </div>
    <?php
}
?>