<?php
function renderInputField(string $label, string $placeholder, string $type = "text", string $name = "", string $id = "", bool $required = true, string $customClasses = ''): void
{
    // Default input wrapper classes
    $defaultWrapperClasses = 'self-stretch mb-md:p-5 p-4 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-start items-center gap-20 overflow-hidden';

    // Merge default classes with custom classes using twMerge
    $wrapperClasses = twMerge($defaultWrapperClasses, $customClasses);

    ?>
    <div class="flex-1 flex flex-col justify-start items-start gap-4">
        <?php if (!empty($label)): ?>
            <label for="<?= htmlspecialchars($id) ?>"
                class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                <?= htmlspecialchars($label) ?>
            </label>
        <?php endif; ?>
        <div class="<?= htmlspecialchars($wrapperClasses) ?>">
            <input type="<?= htmlspecialchars($type) ?>" name="<?= htmlspecialchars($name) ?>"
                id="<?= htmlspecialchars($id) ?>" placeholder="<?= htmlspecialchars($placeholder) ?>"
                class="flex-1 text-neutral-400 font-normal leading-relaxed font-manrope bg-transparent border-none outline-none mb-md:text-lg text-sm"
                <?= $required ? 'required' : '' ?> />
        </div>
    </div>
<?php }