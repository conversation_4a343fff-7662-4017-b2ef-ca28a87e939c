<?php

function renderCountrySelector(
    string $label = "Country",
    string $name = "country",
    string $id = "",
    bool $required = true,
    string $defaultCountry = "Morocco",
    string $customClasses = ''
): void {
    // Generate unique ID if not provided
    $uniqueId = !empty($id) ? $id : $name . '_' . uniqid();

    // Load countries data
    $jsonPath = ROOT_FOLDER . 'json/countries.json';

    if (!file_exists($jsonPath)) {
        echo "<div class='text-red-500'>countries.json not found at: $jsonPath</div>";
        return;
    }

    $jsonData = file_get_contents($jsonPath);
    $countries = json_decode($jsonData, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        echo "<div class='text-red-500'>Invalid JSON in countries.json</div>";
        return;
    }

    // Find default country
    $selectedCountry = $countries[0]; // Fallback to first country
    foreach ($countries as $country) {
        if ($country['name'] === $defaultCountry) {
            $selectedCountry = $country;
            break;
        }
    }

    // Default wrapper classes
    $defaultWrapperClasses = 'relative w-full';
    $wrapperClasses = twMerge($defaultWrapperClasses, $customClasses);

    // Button classes
    $buttonClasses = 'w-full mb-md:p-5 p-4 bg-neutral-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-between items-center gap-4 hover:bg-neutral-800 focus:outline-none focus:ring-2 focus:ring-red-600 transition-colors duration-200 cursor-pointer';

    ?>
    <div class="flex-1 flex flex-col justify-start items-start gap-4">
        <?php if (!empty($label)): ?>
            <label for="<?= htmlspecialchars($uniqueId) ?>"
                class="text-white mb-md:text-lg text-base font-semibold leading-relaxed font-manrope">
                <?= htmlspecialchars($label) ?>
            </label>
        <?php endif; ?>

        <div class="<?= htmlspecialchars($wrapperClasses) ?>">
            <!-- Selected Country Button -->
            <button type="button" id="<?= htmlspecialchars($uniqueId) ?>-button"
                class="<?= htmlspecialchars($buttonClasses) ?>" aria-haspopup="listbox" aria-expanded="false"
                aria-labelledby="<?= htmlspecialchars($uniqueId) ?>-label">
                <div class="flex items-center gap-3">
                    <img id="<?= htmlspecialchars($uniqueId) ?>-flag"
                        src="<?= htmlspecialchars($selectedCountry['flags']['png']) ?>"
                        alt="<?= htmlspecialchars($selectedCountry['name']) ?> flag"
                        class="mb-md:w-8 w-6 mb-md:h-6 h-4 object-cover rounded-sm flex-shrink-0">
                    <span id="<?= htmlspecialchars($uniqueId) ?>-text"
                        class="text-neutral-400 font-normal leading-relaxed font-manrope mb-md:text-lg text-sm text-left">
                        <?= htmlspecialchars($selectedCountry['name']) ?>
                    </span>
                </div>
                <?= icon("ic:outline-keyboard-arrow-down", "text-neutral-400 mb-md:text-xl text-lg transition-transform duration-200") ?>
            </button>

            <!-- Hidden Input -->
            <input type="hidden" name="<?= htmlspecialchars($name) ?>" id="<?= htmlspecialchars($uniqueId) ?>"
                value="<?= htmlspecialchars($selectedCountry['name']) ?>" <?= $required ? 'required' : '' ?>>

            <!-- Dropdown Menu -->
            <div id="<?= htmlspecialchars($uniqueId) ?>-menu"
                class="hidden absolute z-50 mt-2 w-full max-h-60 bg-neutral-900 rounded-lg shadow-xl ring-1 ring-black ring-opacity-5 overflow-hidden"
                role="listbox" aria-labelledby="<?= htmlspecialchars($uniqueId) ?>-label">

                <!-- Search Input -->
                <div class="p-3 border-b border-neutral-800">
                    <input type="text" id="<?= htmlspecialchars($uniqueId) ?>-search" placeholder="Search countries..."
                        class="w-full px-3 py-2 bg-neutral-800 text-white placeholder-neutral-400 rounded-md border-none outline-none focus:ring-2 focus:ring-red-600 mb-md:text-base text-sm">
                </div>

                <!-- Countries List -->
                <div class="max-h-48 overflow-y-auto">
                    <?php foreach ($countries as $country): ?>
                        <div class="country-option flex items-center gap-3 px-4 py-3 text-white cursor-pointer hover:bg-neutral-800 transition-colors duration-150"
                            data-country-name="<?= htmlspecialchars($country['name']) ?>"
                            data-country-flag="<?= htmlspecialchars($country['flags']['png']) ?>"
                            data-country-code="<?= htmlspecialchars($country['countryCallingCode']) ?>" role="option"
                            tabindex="-1">
                            <img src="<?= htmlspecialchars($country['flags']['png']) ?>"
                                alt="<?= htmlspecialchars($country['name']) ?> flag"
                                class="mb-md:w-6 w-5 mb-md:h-4 h-3 object-cover rounded-sm flex-shrink-0">
                            <span class="mb-md:text-base text-sm font-normal leading-relaxed font-manrope">
                                <?= htmlspecialchars($country['name']) ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- No Results Message -->
                <div id="<?= htmlspecialchars($uniqueId) ?>-no-results"
                    class="hidden px-4 py-6 text-center text-neutral-400 mb-md:text-base text-sm">
                    No countries found
                </div>
            </div>
        </div>
    </div>
    <?php
}
?>