<?php

function renderSecondaryButton(string $label, string $href = "#", string $target = "_self", string $customClasses = ''): void
{
    // Default secondary button classes
    $defaultClasses = 'w-full px-6 py-4 bg-white hover:bg-gray-100 hover:scale-105 transition-all duration-300 ease-in-out rounded-lg flex justify-center items-center gap-2.5';

    // Merge default classes with custom classes using twMerge
    $buttonClasses = twMerge($defaultClasses, $customClasses);

    ?>
    <a href="<?= htmlspecialchars($href) ?>" target="<?= htmlspecialchars($target) ?>"
        class="<?= htmlspecialchars($buttonClasses) ?>">
        <span class="text-stone-950 mb-md:text-lg text-sm font-semibold font-manrope leading-none">
            <?= htmlspecialchars($label) ?>
        </span>
    </a>
<?php } ?>