<?php

function renderSaveButton(string $label, string $customClasses = ''): void
{
    // Default save button classes
    $defaultClasses = 'w-40 px-6 py-3.5 bg-red-600 hover:bg-red-500 rounded-[10px] flex justify-center items-center gap-2.5 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed';

    // Merge default classes with custom classes using twMerge
    $buttonClasses = twMerge($defaultClasses, $customClasses);

    ?>
    <button type="submit" class="<?= htmlspecialchars($buttonClasses) ?>">
        <div class="justify-start text-white text-lg font-medium font-['Manrope'] leading-relaxed">
            <?= htmlspecialchars($label) ?>
        </div>
    </button>
<?php } ?>
