<?php

function renderCancelButton(string $label, string $href = "#", string $customClasses = ''): void
{
    // Default cancel button classes
    $defaultClasses = 'w-40 px-6 py-3.5 bg-stone-900 hover:bg-stone-800 rounded-[10px] outline outline-2 outline-neutral-800 hover:outline-neutral-700 flex justify-center items-center gap-2.5 transition-all duration-300';

    // Merge default classes with custom classes using twMerge
    $buttonClasses = twMerge($defaultClasses, $customClasses);

    ?>
    <a href="<?= htmlspecialchars($href) ?>" class="<?= htmlspecialchars($buttonClasses) ?>">
        <div class="justify-start text-white text-lg font-medium font-['Manrope'] leading-relaxed">
            <?= htmlspecialchars($label) ?>
        </div>
    </a>
<?php } ?>
