<?php

function renderSubmitButton(string $label, string $customClasses = ''): void
{
    // Default button classes
    $defaultClasses = 'max-mb-xl:flex-1 px-6 py-4 bg-red-600 rounded-lg flex justify-center items-center gap-2.5 hover:bg-red-400 transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed';

    // Merge default classes with custom classes using twMerge
    $buttonClasses = twMerge($defaultClasses, $customClasses);

    ?>
    <button type="submit" class="<?= htmlspecialchars($buttonClasses) ?>">
        <span class="text-white mb-md:text-lg text-sm font-semibold font-manrope leading-none">
            <?= htmlspecialchars($label) ?>
        </span>
    </button>
<?php } ?>