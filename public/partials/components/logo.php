<?php

function renderLogo(
    bool $isMobile = false,
    string $customClasses = '',
    bool $showText = true,
    string $textSize = 'text-2xl',
    string $href = '/'
): void {
    // Default logo container classes
    $defaultClasses = 'flex justify-start items-center gap-1.5 overflow-hidden';

    // Merge default classes with custom classes using twMerge
    $logoClasses = twMerge($defaultClasses, $customClasses);

    // Determine which logo to use
    $logoSrc = $isMobile ? 'mb-logo.svg' : 'logo.svg';

    // Determine minimum width for logo container
    $minWidth = $isMobile ? '' : 'min-w-[52px]';
    ?>
    <a href="<?= htmlspecialchars($href) ?>" class="<?= htmlspecialchars($logoClasses) ?>">
        <div class="<?= $minWidth ?>">
            <img src="<?= IMAGE_SRC ?><?= $logoSrc ?>" alt="NetflexCheap Logo">
        </div>
        <?php if ($showText): ?>
            <div>
                <span class="<?= htmlspecialchars($textSize) ?> text-white font-semibold"><span
                        class="font-bold">Netflix</span>Cheap</span>
            </div>
        <?php endif; ?>
    </a>
    <?php
}

?>