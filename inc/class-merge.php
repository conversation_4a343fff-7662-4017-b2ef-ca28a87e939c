<?php

/**
 * Simple PHP implementation of class merging similar to tailwind-merge
 * Merges Tailwind CSS classes and resolves conflicts by giving priority to later classes
 */
function twMerge(...$classLists): string
{
    $allClasses = [];
    
    // Flatten all class lists into a single array
    foreach ($classLists as $classList) {
        if (is_string($classList) && !empty(trim($classList))) {
            $classes = preg_split('/\s+/', trim($classList));
            $allClasses = array_merge($allClasses, $classes);
        }
    }
    
    // Remove empty classes
    $allClasses = array_filter($allClasses, function($class) {
        return !empty(trim($class));
    });
    
    // Simple conflict resolution - later classes override earlier ones
    // This handles common Tailwind patterns like padding, margin, background, etc.
    $conflictGroups = [
        // Padding
        'p-' => '/^p-/',
        'px-' => '/^px-/',
        'py-' => '/^py-/',
        'pt-' => '/^pt-/',
        'pr-' => '/^pr-/',
        'pb-' => '/^pb-/',
        'pl-' => '/^pl-/',
        
        // Margin
        'm-' => '/^m-/',
        'mx-' => '/^mx-/',
        'my-' => '/^my-/',
        'mt-' => '/^mt-/',
        'mr-' => '/^mr-/',
        'mb-' => '/^mb-/',
        'ml-' => '/^ml-/',
        
        // Background
        'bg-' => '/^bg-(?!opacity)/',
        
        // Text color
        'text-' => '/^text-(?!xs|sm|base|lg|xl|\d|left|center|right)/',
        
        // Width & Height
        'w-' => '/^w-/',
        'h-' => '/^h-/',
        
        // Display
        'block' => '/^(block|inline|inline-block|flex|inline-flex|grid|inline-grid|hidden)$/',
        
        // Position
        'static' => '/^(static|fixed|absolute|relative|sticky)$/',
        
        // Flex direction
        'flex-row' => '/^flex-(row|col)/',
        
        // Justify content
        'justify-start' => '/^justify-(start|end|center|between|around|evenly)$/',
        
        // Align items
        'items-start' => '/^items-(start|end|center|baseline|stretch)$/',
        
        // Border radius
        'rounded' => '/^rounded/',
        
        // Font weight
        'font-normal' => '/^font-(thin|extralight|light|normal|medium|semibold|bold|extrabold|black)$/',
        
        // Font size
        'text-xs' => '/^text-(xs|sm|base|lg|xl|\dxl)$/',
    ];
    
    $resolvedClasses = [];
    $usedGroups = [];
    
    // Process classes in reverse order (later classes take precedence)
    foreach (array_reverse($allClasses) as $class) {
        $added = false;
        
        foreach ($conflictGroups as $groupKey => $pattern) {
            if (preg_match($pattern, $class)) {
                if (!isset($usedGroups[$groupKey])) {
                    $resolvedClasses[] = $class;
                    $usedGroups[$groupKey] = true;
                    $added = true;
                    break;
                }
            }
        }
        
        // If class doesn't match any conflict group, add it
        if (!$added) {
            $resolvedClasses[] = $class;
        }
    }
    
    // Reverse back to maintain original order and remove duplicates
    $resolvedClasses = array_reverse($resolvedClasses);
    $resolvedClasses = array_unique($resolvedClasses);
    
    return implode(' ', $resolvedClasses);
}

/**
 * Alias for twMerge to match the JavaScript naming convention
 */
function tw_merge(...$classLists): string
{
    return twMerge(...$classLists);
}
?>
