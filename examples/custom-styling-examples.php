<?php
// Example file demonstrating how to use custom styling with twMerge

// Include the necessary files (normally done in your main application)
require_once __DIR__ . '/../inc/config.php';
require_once __DIR__ . '/../public/partials/header.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Styling Examples</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white p-8">
    <div class="max-w-4xl mx-auto space-y-8">
        <h1 class="text-3xl font-bold mb-8">Custom Styling Examples with twMerge</h1>
        
        <!-- Example 1: Default Submit Button -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold">1. Default Submit Button</h2>
            <?php renderSubmitButton("Default Button") ?>
        </div>
        
        <!-- Example 2: Custom Background Color -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold">2. Custom Background Color (like your example)</h2>
            <?php renderSubmitButton("Custom Red Button", "p-3 bg-[#B91C1C] hover:bg-[#991B1B]") ?>
        </div>
        
        <!-- Example 3: Different Padding -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold">3. Custom Padding</h2>
            <?php renderSubmitButton("Large Padding", "px-8 py-6") ?>
        </div>
        
        <!-- Example 4: Green Theme -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold">4. Green Theme</h2>
            <?php renderSubmitButton("Green Button", "bg-green-600 hover:bg-green-500") ?>
        </div>
        
        <!-- Example 5: Rounded and Shadow -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold">5. Extra Rounded with Shadow</h2>
            <?php renderSubmitButton("Rounded Button", "rounded-full shadow-xl") ?>
        </div>
        
        <!-- Example 6: Custom Input Field -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold">6. Custom Input Field</h2>
            <?php renderInputField("Custom Input", "Enter text...", "text", "custom_input", "custom-input", true, "border-2 border-blue-500 bg-blue-900/20") ?>
        </div>
        
        <!-- Example 7: Custom Password Field -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold">7. Custom Password Field</h2>
            <?php renderPasswordField("Custom Password", "Enter password...", "custom_password", "custom-password", true, "border-2 border-purple-500 bg-purple-900/20") ?>
        </div>
        
        <!-- Example 8: Custom Secondary Button -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold">8. Custom Secondary Button</h2>
            <?php renderSecondaryButton("Custom Secondary", "#", "_self", "bg-blue-600 text-white hover:bg-blue-500") ?>
        </div>
        
        <!-- Example 9: Demonstrating Class Conflict Resolution -->
        <div class="space-y-4">
            <h2 class="text-xl font-semibold">9. Class Conflict Resolution Demo</h2>
            <p class="text-gray-400 text-sm">This button shows how twMerge resolves conflicts - the custom padding (p-8) overrides the default padding (px-6 py-4)</p>
            <?php renderSubmitButton("Conflict Resolution", "p-8 bg-purple-600 hover:bg-purple-500") ?>
        </div>
        
        <!-- PHP Code Examples -->
        <div class="space-y-4 mt-12">
            <h2 class="text-2xl font-semibold">PHP Code Examples</h2>
            
            <div class="bg-gray-800 p-4 rounded-lg">
                <h3 class="text-lg font-semibold mb-2">Basic Usage:</h3>
                <pre class="text-green-400"><code>&lt;?php renderSubmitButton("My Button", "bg-blue-600 hover:bg-blue-500") ?&gt;</code></pre>
            </div>
            
            <div class="bg-gray-800 p-4 rounded-lg">
                <h3 class="text-lg font-semibold mb-2">Complex Styling:</h3>
                <pre class="text-green-400"><code>&lt;?php renderSubmitButton("Complex Button", "p-3 bg-[#B91C1C] hover:bg-[#991B1B] shadow-lg rounded-full border-2 border-red-400") ?&gt;</code></pre>
            </div>
            
            <div class="bg-gray-800 p-4 rounded-lg">
                <h3 class="text-lg font-semibold mb-2">Input Field with Custom Styling:</h3>
                <pre class="text-green-400"><code>&lt;?php renderInputField("Email", "Enter email", "email", "email", "email", true, "border-2 border-blue-500 bg-blue-900/20") ?&gt;</code></pre>
            </div>
        </div>
        
        <!-- How twMerge Works -->
        <div class="space-y-4 mt-12">
            <h2 class="text-2xl font-semibold">How twMerge Works</h2>
            <div class="bg-gray-800 p-6 rounded-lg space-y-4">
                <p class="text-gray-300">The <code class="text-yellow-400">twMerge</code> function intelligently merges Tailwind CSS classes:</p>
                <ul class="list-disc list-inside space-y-2 text-gray-300">
                    <li><strong>Conflict Resolution:</strong> Later classes override earlier ones (e.g., <code class="text-yellow-400">p-4</code> overrides <code class="text-yellow-400">px-6 py-4</code>)</li>
                    <li><strong>Smart Grouping:</strong> Recognizes related classes like padding, margin, background, etc.</li>
                    <li><strong>Preserves Order:</strong> Non-conflicting classes maintain their original order</li>
                    <li><strong>Removes Duplicates:</strong> Eliminates duplicate classes automatically</li>
                </ul>
                
                <div class="mt-4">
                    <h4 class="text-lg font-semibold mb-2">Example:</h4>
                    <pre class="text-green-400 bg-gray-900 p-3 rounded"><code>twMerge('px-6 py-4 bg-red-600 hover:bg-red-500', 'p-3 bg-[#B91C1C] shadow-lg')
// Result: 'p-3 bg-[#B91C1C] hover:bg-red-500 shadow-lg'</code></pre>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
