{"name": "Buildio", "version": "1.0.0", "description": "A simple HTML, SCSS, and TypeScript project.", "main": "index.js", "scripts": {"dev": "vite --host", "build": "vite build", "sync": "./server-cli.sh"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "postcss-pxtorem": "^6.1.0", "swiper": "^8.4.7", "tailwind-merge": "^3.3.1", "tailwindcss-animated": "^2.0.0", "vite-plugin-full-reload": "^1.2.0"}, "devDependencies": {"@minko-fe/postcss-pxtorem": "^1.5.0", "@types/node": "^24.0.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.5", "sass": "^1.89.2", "tailwindcss": "^3.4.17", "typescript": "^4.9.5", "vite": "^6.3.5", "vite-plugin-live-reload": "^3.0.4"}}