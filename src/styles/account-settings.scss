// Account Settings Page Styles

.settings-section {
    transition: opacity 0.3s ease-in-out;
    
    &.hidden {
        display: none;
    }
}

.settings-nav-btn {
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    &.active {
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.25);
    }
}

.password-toggle {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    
    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

.password-requirement {
    transition: all 0.3s ease;
    
    iconify-icon {
        transition: all 0.3s ease;
    }
}

// Form input focus states
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="date"],
input[type="password"] {
    transition: all 0.2s ease;
    
    &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
    }
    
    &:invalid {
        box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
    }
}

// Loading states
button[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover {
        transform: none !important;
    }
}

// Auto-save indicator
.auto-save-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(34, 197, 94, 0.9);
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    
    &.show {
        opacity: 1;
        transform: translateY(0);
    }
}

// Responsive adjustments
@media (max-width: 768px) {
    .settings-nav-btn {
        font-size: 16px;
        padding: 12px 16px;
    }
    
    .password-requirement {
        font-size: 12px;
    }
}

// Dark theme enhancements
.bg-neutral-900 {
    &:hover {
        background-color: rgb(38 38 38);
    }
    
    &:focus-within {
        background-color: rgb(38 38 38);
        outline: 1px solid rgb(115 115 115);
    }
}

// Animation for section switching
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.settings-section:not(.hidden) {
    animation: fadeIn 0.3s ease-out;
}
