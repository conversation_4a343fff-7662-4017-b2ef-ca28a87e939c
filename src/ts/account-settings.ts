document.addEventListener('DOMContentLoaded', function () {
    // Password strength validation
    const newPasswordInput = document.getElementById('new-password') as HTMLInputElement;
    const confirmPasswordInput = document.getElementById('confirm-password') as HTMLInputElement;
    
    if (newPasswordInput && confirmPasswordInput) {
        // Password strength requirements
        const requirements = {
            length: (password: string) => password.length >= 8,
            uppercase: (password: string) => /[A-Z]/.test(password),
            lowercase: (password: string) => /[a-z]/.test(password),
            number: (password: string) => /\d/.test(password)
        };

        // Update requirement indicators
        function updateRequirements(password: string) {
            const requirementElements = document.querySelectorAll('.password-requirement');
            requirementElements.forEach((element, index) => {
                const icon = element.querySelector('iconify-icon');
                const requirementKeys = Object.keys(requirements);
                const isValid = requirements[requirementKeys[index] as keyof typeof requirements](password);
                
                if (icon) {
                    if (isValid) {
                        icon.setAttribute('icon', 'mdi:check-circle');
                        icon.className = 'text-green-500 text-sm';
                    } else {
                        icon.setAttribute('icon', 'mdi:check-circle-outline');
                        icon.className = 'text-neutral-500 text-sm';
                    }
                }
            });
        }

        // Validate password match
        function validatePasswordMatch() {
            const newPassword = newPasswordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            
            if (confirmPassword && newPassword !== confirmPassword) {
                confirmPasswordInput.setCustomValidity('Passwords do not match');
                confirmPasswordInput.classList.add('border-red-500');
            } else {
                confirmPasswordInput.setCustomValidity('');
                confirmPasswordInput.classList.remove('border-red-500');
            }
        }

        // Event listeners
        newPasswordInput.addEventListener('input', function() {
            updateRequirements(this.value);
            validatePasswordMatch();
        });

        confirmPasswordInput.addEventListener('input', validatePasswordMatch);
    }

    // Form submission handling
    const profileForm = document.querySelector('form[action="/account/settings/update"]') as HTMLFormElement;
    const passwordForm = document.querySelector('form[action="/account/settings/change-password"]') as HTMLFormElement;

    if (profileForm) {
        profileForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Basic validation
            const formData = new FormData(this);
            const fullName = formData.get('full_name') as string;
            const email = formData.get('email') as string;
            
            if (!fullName.trim()) {
                alert('Please enter your full name');
                return;
            }
            
            if (!email.trim() || !isValidEmail(email)) {
                alert('Please enter a valid email address');
                return;
            }
            
            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]') as HTMLButtonElement;
            const originalText = submitButton.textContent;
            submitButton.textContent = 'Saving...';
            submitButton.disabled = true;
            
            // Simulate API call (replace with actual implementation)
            setTimeout(() => {
                alert('Profile updated successfully!');
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            }, 1500);
        });
    }

    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const currentPassword = formData.get('current_password') as string;
            const newPassword = formData.get('new_password') as string;
            const confirmPassword = formData.get('confirm_password') as string;
            
            // Validate all requirements
            const allRequirementsMet = Object.values(requirements).every(req => req(newPassword));
            
            if (!currentPassword.trim()) {
                alert('Please enter your current password');
                return;
            }
            
            if (!allRequirementsMet) {
                alert('Please ensure your new password meets all requirements');
                return;
            }
            
            if (newPassword !== confirmPassword) {
                alert('New password and confirmation do not match');
                return;
            }
            
            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]') as HTMLButtonElement;
            const originalText = submitButton.textContent;
            submitButton.textContent = 'Updating...';
            submitButton.disabled = true;
            
            // Simulate API call (replace with actual implementation)
            setTimeout(() => {
                alert('Password updated successfully!');
                this.reset();
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            }, 1500);
        });
    }

    // Email validation helper
    function isValidEmail(email: string): boolean {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Auto-save functionality for profile form
    const profileInputs = document.querySelectorAll('#profile-section input, #profile-section select');
    let autoSaveTimeout: NodeJS.Timeout;

    profileInputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                // Auto-save draft (implement as needed)
                console.log('Auto-saving profile changes...');
            }, 2000);
        });
    });

    // Country code dropdown integration
    const countryCodeButton = document.getElementById('country-code-button');
    if (countryCodeButton) {
        // Ensure country code is properly integrated with phone number field
        const phoneInput = document.getElementById('phone-number') as HTMLInputElement;
        const callingCodeInput = document.getElementById('calling-code') as HTMLInputElement;
        
        if (phoneInput && callingCodeInput) {
            phoneInput.addEventListener('input', function() {
                // Format phone number as user types
                let value = this.value.replace(/\D/g, '');
                if (value.length > 0) {
                    // Add formatting based on country code
                    // This is a simple example - you might want more sophisticated formatting
                    if (value.length > 3) {
                        value = value.slice(0, 3) + '-' + value.slice(3);
                    }
                    if (value.length > 7) {
                        value = value.slice(0, 7) + '-' + value.slice(7, 11);
                    }
                }
                this.value = value;
            });
        }
    }
});
