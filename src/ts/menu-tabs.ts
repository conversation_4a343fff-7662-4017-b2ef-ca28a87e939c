function showSection(sectionName: string): void {
    // Hide all sections
    document.querySelectorAll('.settings-section').forEach((section: Element) => {
        section.classList.add('hidden');
    });

    // Show selected section
    const selectedSection = document.getElementById(sectionName + '-section');
    if (selectedSection) selectedSection.classList.remove('hidden');

    // Update navigation buttons
    document.querySelectorAll('.settings-nav-btn').forEach((btn: Element) => {
        // Remove active state classes
        btn.classList.remove('active', 'bg-red-600', 'outline-red-500');
        // Add inactive state classes
        btn.classList.add('bg-stone-900', 'outline-neutral-800');

        // Re-add hover and focus classes to inactive tabs
        const hoverClasses = ['hover:bg-stone-800', 'hover:outline-neutral-600', 'hover:shadow-md', 'hover:shadow-neutral-900/50'];
        const focusClasses = ['focus:bg-stone-800', 'focus:outline-neutral-600', 'focus:shadow-md', 'focus:shadow-neutral-900/50'];
        btn.classList.add(...hoverClasses, ...focusClasses);
    });

    // Activate selected button
    const activeBtn = document.querySelector(`[data-section="${sectionName}"]`) as HTMLElement;
    if (activeBtn) {
        // Add active state classes
        activeBtn.classList.add('active', 'bg-red-600', 'outline-red-500');
        activeBtn.classList.remove('bg-stone-900', 'outline-neutral-800');

        // Remove hover and focus classes from active tab
        const hoverClasses = ['hover:bg-stone-800', 'hover:outline-neutral-600', 'hover:shadow-md', 'hover:shadow-neutral-900/50'];
        const focusClasses = ['focus:bg-stone-800', 'focus:outline-neutral-600', 'focus:shadow-md', 'focus:shadow-neutral-900/50'];
        activeBtn.classList.remove(...hoverClasses, ...focusClasses);

        // Keep only focus states for accessibility on active tab (but different styling)
        activeBtn.classList.add('focus:bg-red-500', 'focus:outline-red-400', 'focus:shadow-lg', 'focus:shadow-red-600/20');
    }
}

// Initialize tabs on page load
function initializeTabs(): void {
    // Add hover and focus classes to all inactive tabs initially
    document.querySelectorAll('.settings-nav-btn:not(.active)').forEach((btn: Element) => {
        const hoverClasses = ['hover:bg-stone-800', 'hover:outline-neutral-600', 'hover:shadow-md', 'hover:shadow-neutral-900/50'];
        const focusClasses = ['focus:bg-stone-800', 'focus:outline-neutral-600', 'focus:shadow-md', 'focus:shadow-neutral-900/50'];
        btn.classList.add(...hoverClasses, ...focusClasses);
    });
}

// Run initialization when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeTabs);

// Make functions available globally
(window as any).showSection = showSection;
